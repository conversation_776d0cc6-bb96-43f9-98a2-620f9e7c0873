using System.Collections.Generic;
using System.Linq; // 确保引入 LINQ
using Poesis.Ontology.Behaviors; // 引入新的命名空间
using UnityEngine;

namespace Poesis.Ontology
{
    /// <summary>
    /// 概念定义类，用于定义游戏中的概念
    /// </summary>
    [CreateAssetMenu(fileName = "Concept", menuName = "Game Design Language/Concept")]
    public class ConceptDefinition : Definition
    {
        /// <summary>
        /// 概念的标签，用于分类和查询
        /// </summary>
        public List<string> tags = new List<string>();
        /// <summary>
        /// 概念名称
        /// </summary>
        public string displayName;
        /// <summary>
        /// 父概念，用于继承属性
        /// </summary>
        public ConceptDefinition parentConcept;

        /// <summary>
        /// 概念的属性列表
        /// </summary>
        /// <summary>
        /// 概念的属性列表，包含属性定义和其在该概念中的具体值
        /// </summary>
        public List<ConceptProperty> properties = new List<ConceptProperty>();

        /// <summary>
        /// 概念的特性列表
        /// </summary>
        [SerializeReference] public List<FeatureDefinition> features = new List<FeatureDefinition>();

        /// <summary>
        /// 域关系列表，表示当前概念作为域与其他概念建立的关系
        /// </summary>
        public List<RelationDefinition> domainRelations = new List<RelationDefinition>();

        /// <summary>
        /// 内部关系列表，直接在概念内部定义的关系
        /// </summary>
        public List<InternalRelation> internalRelations = new List<InternalRelation>();

        /// <summary>
        /// 图标预览的背景颜色
        /// </summary>
        public Color backgroundColor = Color.clear; // 默认设置为透明

        /// <summary>
        /// 概念的行为列表
        /// </summary>
        [SerializeReference] public List<ConceptBehavior> behaviors = new List<ConceptBehavior>();


        public string DisplayName => displayName == "" ? name : displayName;

        /// <summary>
        /// 获取所有属性，包括从父概念继承的属性
        /// </summary>
        /// <returns>所有属性的列表</returns>
        public virtual IEnumerable<PropertyDefinition> GetAllProperties()
        {
            // 使用字典来处理属性覆盖，子概念的属性优先
            Dictionary<string, PropertyDefinition> propertyMap = new Dictionary<string, PropertyDefinition>();

            // 1. 添加父概念的属性
            if (parentConcept != null)
            {
                foreach (var prop in parentConcept.GetAllProperties())
                {
                    propertyMap[prop.name] = prop;
                }
            }

            // 2. 添加当前概念自身的属性定义（如果存在同名属性，则覆盖父概念的）
            foreach (var conceptProp in properties)
            {
                if (conceptProp != null && conceptProp.propertyDefinition != null)
                {
                    propertyMap[conceptProp.propertyDefinition.name] = conceptProp.propertyDefinition;
                }
            }

            // 3. 添加所有 FeatureDefinition 的属性
            foreach (var feature in features)
            {
                if (feature != null)
                {
                    foreach (var featureProp in feature.GetAllProperties())
                    {
                        propertyMap[featureProp.name] = featureProp;
                    }
                }
            }

            return propertyMap.Values;
        }

        /// <summary>
        /// 获取所有行为，包括从父概念继承的行为。
        /// 子概念中具有相同 behaviorId 的行为将覆盖父概念的行为。
        /// </summary>
        /// <returns>所有行为的列表</returns>
        public virtual IEnumerable<ConceptBehavior> GetAllBehaviors()
        {
            Dictionary<string, ConceptBehavior> behaviorMap = new Dictionary<string, ConceptBehavior>();

            // 1. 添加父概念的行为
            if (parentConcept != null)
            {
                foreach (var behavior in parentConcept.GetAllBehaviors())
                {
                    if (!string.IsNullOrEmpty(behavior.behaviorId))
                    {
                        behaviorMap[behavior.behaviorId] = behavior;
                    }
                    else
                    {
                        // 如果没有ID，则直接添加，不参与覆盖
                        // 注意：这里使用GUID是为了确保唯一性，但如果行为是共享的，可能需要更复杂的逻辑
                        behaviorMap[System.Guid.NewGuid().ToString()] = behavior;
                    }
                }
            }

            // 2. 添加当前概念自身的行为（覆盖父概念的同ID行为）
            foreach (var behavior in behaviors)
            {
                if (behavior == null)
                {
                    Debug.LogWarning($"ConceptDefinition '{name}' has a null ConceptBehavior in its list.");
                    continue;
                }

                if (!string.IsNullOrEmpty(behavior.behaviorId))
                {
                    behaviorMap[behavior.behaviorId] = behavior;
                }
                else
                {
                    // 如果没有ID，则直接添加，不参与覆盖
                    behaviorMap[System.Guid.NewGuid().ToString()] = behavior;
                }
            }

            return behaviorMap.Values;
        }

        /// <summary>
        /// 检查是否是指定概念的子类
        /// </summary>
        /// <param name="potentialParent">潜在的父概念</param>
        /// <returns>是否是子类</returns>
        public bool IsSubclassOf(ConceptDefinition potentialParent)
        {
            if (potentialParent == null)
                return false;

            if (this == potentialParent)
                return true;

            if (parentConcept != null)
                return parentConcept.IsSubclassOf(potentialParent);

            return false;
        }

        /// <summary>
        /// 添加域关系
        /// </summary>
        /// <param name="relation">关系定义</param>
        public void AddDomainRelation(RelationDefinition relation)
        {
            if (!domainRelations.Contains(relation))
            {
                domainRelations.Add(relation);
            }
        }

        /// <summary>
        /// 添加内部关系
        /// </summary>
        /// <param name="relation">内部关系</param>
        public void AddInternalRelation(InternalRelation relation)
        {
            internalRelations.Add(relation);
        }

        /// <summary>
        /// 创建并添加内部关系
        /// </summary>
        /// <param name="name">关系名称</param>
        /// <param name="targetConcept">目标概念</param>
        /// <param name="description">关系描述</param>
        /// <returns>创建的内部关系</returns>
        public InternalRelation CreateInternalRelation(string name, ConceptDefinition targetConcept, string description = "")
        {
            InternalRelation relation = InternalRelation.Create(name, targetConcept, description);
            internalRelations.Add(relation);
            return relation;
        }

        /// <summary>
        /// 获取所有域关系，包括从父概念继承的域关系
        /// </summary>
        /// <returns>所有域关系的列表</returns>
        public List<RelationDefinition> GetAllDomainRelations()
        {
            List<RelationDefinition> allRelations = new List<RelationDefinition>(domainRelations);

            // 递归获取父概念的域关系
            if (parentConcept != null)
            {
                allRelations.AddRange(parentConcept.GetAllDomainRelations());
            }

            return allRelations;
        }

        /// <summary>
        /// 获取所有内部关系，包括从父概念继承的内部关系
        /// </summary>
        /// <returns>所有内部关系的列表</returns>
        public List<InternalRelation> GetAllInternalRelations()
        {
            List<InternalRelation> allRelations = new List<InternalRelation>(internalRelations);

            // 递归获取父概念的内部关系
            if (parentConcept != null)
            {
                allRelations.AddRange(parentConcept.GetAllInternalRelations());
            }

            return allRelations;
        }
    }
}
