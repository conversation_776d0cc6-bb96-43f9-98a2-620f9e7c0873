using System; // 添加此行，解决 Type 和 Activator 错误
using System.Collections.Generic;
using System.IO;
using System.Linq; // 添加此行，支持ToList()方法
using Poesis.Ontology;
using Poesis.Ontology.Editor.UI; // 引入新的 UI 元素命名空间
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

namespace Poesis.Ontology.Editor
{
    [CustomEditor(typeof(ConceptDefinition))]
    public class ConceptDefinitionEditor : UnityEditor.Editor
    {
        private VisualElement root;
        private ConceptDefinition conceptDefinition;
        private SerializedObject serializedObject;

        public override VisualElement CreateInspectorGUI()
        {
            // 获取目标对象
            conceptDefinition = (ConceptDefinition)target;
            serializedObject = new SerializedObject(target);

            // 创建根元素
            root = new VisualElement();
            root.styleSheets.Add(AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Poesis/Ontology/Editor/UI/ConceptDefinitionEditor.uss"));

            // 添加标题
            var titleLabel = new Label("Concept - " + (string.IsNullOrEmpty(conceptDefinition.displayName) ? conceptDefinition.name : conceptDefinition.displayName));
            titleLabel.AddToClassList("title");
            root.Add(titleLabel);

            // ==== 添加图标字段和预览 =====
            // --- 图标预览和选择 ---
            // 添加图标字段和预览 (使用新的可复用元素)
            var iconElement = new IconVisualElement(conceptDefinition);
            root.Add(iconElement);

            // 添加概念名称字段
            var nameField = new TextField("Display Name");
            nameField.value = conceptDefinition.displayName;
            nameField.RegisterValueChangedCallback(evt =>
            {
                Undo.RecordObject(conceptDefinition, "Change Concept Name");
                conceptDefinition.displayName = evt.newValue;
                EditorUtility.SetDirty(conceptDefinition);
            });

            root.Add(nameField);

            // Add Description Field
            var descriptionField = new TextField("Description");
            descriptionField.multiline = true; // Allow multiline input
            descriptionField.value = conceptDefinition.description;
            descriptionField.AddToClassList("description-field"); // 添加自定义类名
            descriptionField.RegisterValueChangedCallback(evt =>
            {
                Undo.RecordObject(conceptDefinition, "Change Concept Description");
                conceptDefinition.description = evt.newValue;
                EditorUtility.SetDirty(conceptDefinition);
            });
            root.Add(descriptionField);

            // 添加父概念字段
            var parentField = new ObjectField("父概念");
            parentField.objectType = typeof(ConceptDefinition);
            parentField.value = conceptDefinition.parentConcept;
            parentField.RegisterValueChangedCallback(evt =>
            {
                Undo.RecordObject(conceptDefinition, "Change Parent Concept");
                conceptDefinition.parentConcept = (ConceptDefinition)evt.newValue;
                EditorUtility.SetDirty(conceptDefinition);
            });
            root.Add(parentField);

            // 创建属性区域
            var propertiesSection = CreatePropertiesSection();
            root.Add(propertiesSection);
            // 添加特性列表
            var featuresContainer = new VisualElement();
            featuresContainer.AddToClassList("list-container");
            root.Add(featuresContainer);

            var featuresProperty = serializedObject.FindProperty("features");
            var featuresList = new PropertyField(featuresProperty, "特性列表");
            featuresList.RegisterValueChangeCallback(evt =>
            {
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(conceptDefinition);
            });
            featuresContainer.Add(featuresList);

            // 添加行为列表 (新增)
            var behaviorsContainer = new VisualElement();
            behaviorsContainer.AddToClassList("list-container");
            root.Add(behaviorsContainer);

            var behaviorsProperty = serializedObject.FindProperty("behaviors");
            var behaviorsList = new PropertyField(behaviorsProperty, "行为列表");
            behaviorsList.RegisterValueChangeCallback(evt =>
            {
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(conceptDefinition);
            });
            behaviorsContainer.Add(behaviorsList);

            // 添加标签列表
            var tagsContainer = new VisualElement();
            tagsContainer.AddToClassList("list-container");
            root.Add(tagsContainer);

            var tagsProperty = serializedObject.FindProperty("tags");
            var tagsList = new PropertyField(tagsProperty, "标签列表");
            tagsList.RegisterValueChangeCallback(evt =>
            {
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(conceptDefinition);
            });
            tagsContainer.Add(tagsList);

            // 添加域关系列表
            var domainRelationsContainer = new VisualElement();
            domainRelationsContainer.AddToClassList("list-container");
            root.Add(domainRelationsContainer);

            var domainRelationsProperty = serializedObject.FindProperty("domainRelations");
            var domainRelationsList = new PropertyField(domainRelationsProperty, "域关系列表");
            domainRelationsList.RegisterValueChangeCallback(evt =>
            {
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(conceptDefinition);
            });
            domainRelationsContainer.Add(domainRelationsList);

            // 添加内部关系列表
            var internalRelationsContainer = new VisualElement();
            internalRelationsContainer.AddToClassList("list-container");
            root.Add(internalRelationsContainer);

            var internalRelationsProperty = serializedObject.FindProperty("internalRelations");
            var internalRelationsList = new PropertyField(internalRelationsProperty, "内部关系列表");
            internalRelationsList.RegisterValueChangeCallback(evt =>
            {
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(conceptDefinition);
            });
            internalRelationsContainer.Add(internalRelationsList);

            // 监听任何变更，及时应用
            root.RegisterCallback<AttachToPanelEvent>(evt =>
            {
                root.schedule.Execute(() =>
                {
                    serializedObject.Update();
                }).Every(100);
            });

            return root;
        }



        private void OnCreateConceptPropertyClicked()
        {
            // 获取当前概念定义资产所在的目录
            string targetPath = Path.GetDirectoryName(AssetDatabase.GetAssetPath(target));
            string propertiesPath = Path.Combine(targetPath, "Properties");

            if (!Directory.Exists(propertiesPath))
            {
                Directory.CreateDirectory(propertiesPath);
            }

            // 显示属性创建窗口，让用户选择或创建 PropertyDefinition
            PropertyDefinitionCreationWindow.ShowWindow(propertiesPath, (newPropertyDef) =>
            {
                if (newPropertyDef != null)
                {
                    // 检查是否已存在同名属性
                    if (conceptDefinition.properties.Any(p => p.propertyDefinition.name == newPropertyDef.name))
                    {
                        EditorUtility.DisplayDialog("警告",
                            $"当前概念中已存在名为 '{newPropertyDef.name}' 的属性定义。",
                            "确定");
                        return;
                    }

                    // 创建新的属性
                    ConceptProperty newConceptProperty = new ConceptProperty(newPropertyDef);

                    // 更新数据
                    serializedObject.Update();
                    conceptDefinition.properties.Add(newConceptProperty);
                    serializedObject.ApplyModifiedProperties();
                    EditorUtility.SetDirty(conceptDefinition);

                    // 重新创建属性区域
                    var propertiesContainer = root.Q<VisualElement>("properties-container");
                    if (propertiesContainer != null)
                    {
                        var newSection = CreatePropertiesSection();
                        var oldSection = propertiesContainer.parent;
                        if (oldSection != null)
                        {
                            oldSection.parent.Remove(oldSection);
                            oldSection.parent.Add(newSection);
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 创建属性区域，包括继承的属性和本地属性
        /// </summary>
        private VisualElement CreatePropertiesSection()
        {
            var propertiesContainer = new VisualElement { name = "properties-container" };
            propertiesContainer.AddToClassList("list-container");

            // 添加标题栏和创建按钮
            var headerContainer = new VisualElement();
            headerContainer.AddToClassList("property-list-header");

            var propertiesListTitleLabel = new Label("属性列表");
            propertiesListTitleLabel.AddToClassList("property-list-title");
            headerContainer.Add(propertiesListTitleLabel);

            var createButton = new Button(() => OnCreateConceptPropertyClicked()) { text = "创建新属性" };
            createButton.AddToClassList("create-property-button");
            headerContainer.Add(createButton);

            propertiesContainer.Add(headerContainer);

            // 收集所有属性信息
            var propertyGroups = CollectPropertyGroups();

            // 按概念创建属性组
            foreach (var group in propertyGroups)
            {
                var groupElement = new PropertyGroupElement(
                    group.Key,
                    group.Value,
                    conceptDefinition,
                    serializedObject
                );
                propertiesContainer.Add(groupElement);
            }

            return propertiesContainer;
        }

        /// <summary>
        /// 收集所有属性并按来源概念分组
        /// </summary>
        private Dictionary<string, List<DisplayPropertyInfo>> CollectPropertyGroups()
        {
            var groups = new Dictionary<string, List<DisplayPropertyInfo>>();
            var allProperties = conceptDefinition.GetAllProperties();
            var propDict = conceptDefinition.properties.ToDictionary(p => p.propertyDefinition.name);

            // 用于跟踪属性的来源
            var propertySourceMap = new Dictionary<string, ConceptDefinition>();
            var currentConcept = conceptDefinition;

            // 遍历继承链，找出每个属性的最近来源
            while (currentConcept != null)
            {
                foreach (var property in currentConcept.properties)
                {
                    if (property?.propertyDefinition != null &&
                        !propertySourceMap.ContainsKey(property.propertyDefinition.name))
                    {
                        propertySourceMap[property.propertyDefinition.name] = currentConcept;
                    }
                }
                currentConcept = currentConcept.parentConcept;
            }

            // 处理当前概念的属性
            var currentConceptProps = new List<DisplayPropertyInfo>();
            groups["当前概念属性"] = currentConceptProps;

            foreach (var prop in allProperties)
            {
                var sourceConcept = propertySourceMap.TryGetValue(prop.name, out var source)
                    ? source
                    : conceptDefinition;

                propDict.TryGetValue(prop.name, out var conceptProp);
                bool isOverridden = conceptProp != null && sourceConcept != conceptDefinition;

                var displayInfo = new DisplayPropertyInfo(
                    prop,
                    conceptProp?.value,
                    sourceConcept,
                    conceptDefinition,
                    isOverridden
                );

                // 确定属性应该归属的组
                string groupName = sourceConcept == conceptDefinition
                    ? "当前概念属性"
                    : $"继承自 {sourceConcept.DisplayName}";

                if (!groups.ContainsKey(groupName))
                {
                    groups[groupName] = new List<DisplayPropertyInfo>();
                }
                groups[groupName].Add(displayInfo);
            }

            return groups;
        }

        public override Texture2D RenderStaticPreview(string assetPath, UnityEngine.Object[] subAssets, int width, int height)
        {
            ConceptDefinition conceptDefinition = (ConceptDefinition)target;
            if (conceptDefinition == null || conceptDefinition.icon == null)
                return null;

            // conceptDefinition.icon must be a supported format: ARGB32, RGBA32, RGB24, Alpha8 or one of float formats
            Texture2D tex = new Texture2D(width, height);
            EditorUtility.CopySerialized(conceptDefinition.icon, tex);
            return tex;
        }
    }
}
