using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine.UIElements;

namespace Poesis.Ontology.Editor.UI
{
    /// <summary>
    /// 表示一个属性组的 UI 元素，用于显示和管理一组相关的属性
    /// </summary>
    public class PropertyGroupElement : VisualElement
    {
        private readonly string groupName;
        private readonly List<DisplayPropertyInfo> properties;
        private readonly ConceptDefinition conceptDefinition;
        private readonly SerializedObject serializedObject;
        private readonly Foldout foldout;

        public PropertyGroupElement(string name, List<DisplayPropertyInfo> props, ConceptDefinition concept, SerializedObject serializedObj)
        {
            groupName = name;
            properties = props;
            conceptDefinition = concept;
            serializedObject = serializedObj;

            AddToClassList("property-group");

            // 创建折叠面板
            foldout = new Foldout
            {
                text = groupName,
                value = true // 默认展开
            };
            foldout.AddToClassList("property-group__header");
            Add(foldout);

            // 创建属性列表
            CreatePropertyList();
        }

        private void CreatePropertyList()
        {
            foreach (var prop in properties)
            {
                var itemContainer = new VisualElement();
                itemContainer.AddToClassList("property-list-item");

                if (prop.IsInherited)
                {
                    itemContainer.AddToClassList("property-inherited");
                }

                if (prop.IsOverridden)
                {
                    itemContainer.AddToClassList("property-overridden");
                }

                // 添加属性定义字段
                var propertyDefField = new ObjectField("属性定义")
                {
                    objectType = typeof(PropertyDefinition),
                    value = prop.PropertyDefinition
                };
                propertyDefField.SetEnabled(false); // 属性定义字段始终为只读
                itemContainer.Add(propertyDefField);

                // 创建值编辑容器
                var valueContainer = new VisualElement();
                valueContainer.style.flexDirection = FlexDirection.Row;
                valueContainer.style.alignItems = Align.Center;

                // 创建值编辑器容器
                var valueEditorContainer = new VisualElement();
                valueEditorContainer.style.flexGrow = 1;
                valueContainer.Add(valueEditorContainer);

                // 创建重置按钮
                var resetButton = new Button(() => OnResetClicked(prop)) { text = "⟲" };
                resetButton.AddToClassList("reset-button");
                resetButton.tooltip = "重置为" + (prop.IsInherited ? "父概念的值" : "默认值");
                resetButton.SetEnabled(prop.IsOverridden || (!prop.IsInherited && prop.Value != null));
                valueContainer.Add(resetButton);

                itemContainer.Add(valueContainer);

                // 创建类型化的编辑器
                CreateTypedEditor(valueEditorContainer, prop, resetButton);

                // 添加来源指示器
                if (prop.IsInherited)
                {
                    var sourceIndicator = new Label($"继承自 {prop.SourceConcept.displayName ?? prop.SourceConcept.name}");
                    sourceIndicator.AddToClassList("property-source-indicator");
                    itemContainer.Add(sourceIndicator);
                }

                foldout.Add(itemContainer);
            }
        }

        private void CreateTypedEditor(VisualElement container, DisplayPropertyInfo prop, Button resetButton)
        {
            container.Clear();

            // 获取当前值
            object currentValue = prop.Value?.GetValue<object>() ?? prop.PropertyDefinition.DefaultValueAsObject;

            // 创建编辑器
            VisualElement editor = PropertyEditorFactory.CreateEditorForType(
                prop.PropertyDefinition,
                currentValue,
                newValue => OnValueChanged(prop, newValue, resetButton),
                null
            );

            if (editor != null)
            {
                // 如果是继承的且未覆盖，现在允许编辑，因为编辑操作会触发覆盖逻辑
                // 不再在此处设置为只读
                container.Add(editor);
            }
        }

        private void OnValueChanged(DisplayPropertyInfo prop, object newValue, Button resetButton)
        {
            if (prop.IsInherited && !prop.IsOverridden)
            {
                // 创建新的 ConceptProperty 实例来覆盖继承的属性
                ConceptProperty newProperty = new ConceptProperty(prop.PropertyDefinition);
                conceptDefinition.properties.Add(newProperty);
                prop.IsOverridden = true;
            }

            // 更新属性值
            var foundProperty = conceptDefinition.properties.Find(p =>
                p.propertyDefinition == prop.PropertyDefinition);
            if (foundProperty != null)
            {
                foundProperty.value = PropertyValue.Create(prop.PropertyDefinition.displayName, newValue);
            }

            prop.Value = foundProperty?.value;

            // 更新重置按钮状态
            bool isDefault = Equals(newValue, prop.PropertyDefinition.DefaultValueAsObject);
            resetButton.SetEnabled(!isDefault || prop.IsOverridden);

            // 保存更改
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(conceptDefinition);
        }

        private void OnResetClicked(DisplayPropertyInfo prop)
        {
            if (prop.IsInherited && prop.IsOverridden)
            {
                // 移除覆盖的属性
                conceptDefinition.properties.RemoveAll(p => p.propertyDefinition == prop.PropertyDefinition);
                prop.IsOverridden = false;

                // 获取父概念的属性值
                var parentProperty = prop.SourceConcept.properties
                    .FirstOrDefault(p => p.propertyDefinition == prop.PropertyDefinition);
                prop.Value = parentProperty?.value;

                // 查找属性容器
                var container = foldout.Q<VisualElement>(className: "property-list-item")
                    ?.Children()
                    .FirstOrDefault(x =>
                        x.Q<ObjectField>()?.value == prop.PropertyDefinition);

                if (container != null)
                {
                    // 移除覆盖样式
                    container.RemoveFromClassList("property-overridden");
                    container.AddToClassList("property-inherited");

                    // 更新重置按钮状态
                    var resetButton = container.Q<Button>("reset-button");
                    if (resetButton != null)
                    {
                        resetButton.SetEnabled(false);
                        resetButton.tooltip = "重置为父概念的值";
                    }

                    // 刷新值编辑器以显示父概念的值
                    var valueContainer = container.Q<VisualElement>(className: null);
                    if (valueContainer != null)
                    {
                        var editor = valueContainer.Q<VisualElement>();
                        if (editor != null)
                        {
                            valueContainer.Remove(editor);
                            CreateTypedEditor(valueContainer, prop, resetButton);
                        }
                    }
                }
            }
            else
            {
                // 重置为默认值
                var foundProperty = conceptDefinition.properties.Find(p =>
                    p.propertyDefinition == prop.PropertyDefinition);
                if (foundProperty != null)
                {
                    foundProperty.value = null;
                }

                // 设置为默认值
                prop.Value = null;

                // 查找属性容器
                var container = foldout.Q<VisualElement>(className: "property-list-item")
                    ?.Children()
                    .FirstOrDefault(x =>
                        x.Q<ObjectField>()?.value == prop.PropertyDefinition);

                if (container != null)
                {
                    var resetButton = container.Q<Button>("reset-button");
                    resetButton?.SetEnabled(false);

                    // 刷新值编辑器以显示默认值
                    var valueContainer = container.Q<VisualElement>(className: null);
                    if (valueContainer != null)
                    {
                        var editor = valueContainer.Q<VisualElement>();
                        if (editor != null)
                        {
                            valueContainer.Remove(editor);
                            CreateTypedEditor(valueContainer, prop, resetButton);
                        }
                    }
                }
            }

            // 保存更改
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(conceptDefinition);
        }
    }
}